# SerpAPI Debug Export Guide

This guide explains how to use the debug export functionality to analyze SerpAPI responses and troubleshoot the e-commerce product sourcing agent.

## 🔧 Configuration

### Environment Variables

The debug export functionality is controlled by environment variables in your `.env` file:

```bash
# Enable/disable API response exports
EXPORT_API_RESPONSES=true

# Directory for debug exports
DEBUG_EXPORT_DIR=debug_exports

# General debug mode
DEBUG_MODE=true
```

### Enabling/Disabling Exports

- Set `EXPORT_API_RESPONSES=true` to enable exports
- Set `EXPORT_API_RESPONSES=false` to disable exports
- Change `DEBUG_EXPORT_DIR` to customize the export directory

## 📁 Export Files

When enabled, the agent creates two types of export files for each SerpAPI call:

### 1. Raw API Response Files
**Format**: `serpapi_response_{query}_{timestamp}.json`

**Example**: `serpapi_response_wireless_earbuds_electronics_20250828_100241.json`

**Contents**:
```json
{
  "timestamp": "2025-08-28T10:02:41.521465",
  "search_query": "wireless earbuds electronics",
  "raw_response": {
    "search_metadata": { ... },
    "search_parameters": { ... },
    "search_information": { ... },
    "organic_results": [ ... ]
  },
  "metadata": {
    "total_results": 22,
    "has_error": false,
    "search_parameters": { ... },
    "search_information": { ... }
  }
}
```

### 2. Processed Results Files
**Format**: `processed_results_{query}_{timestamp}.json`

**Example**: `processed_results_wireless_earbuds_electronics_20250828_100241.json`

**Contents**:
```json
{
  "timestamp": "2025-08-28T10:02:41.529107",
  "search_query": "wireless earbuds electronics",
  "total_products": 2,
  "processed_products": [
    {
      "title": "Wireless Earbuds...",
      "price": 34.99,
      "rating": 5.0,
      "reviews_count": 298,
      "asin": "B0F9FVRV9K",
      "monthly_sales": 200,
      "trend": "stable",
      "competition_level": "low"
    }
  ],
  "summary": {
    "price_range": { "min": 32.99, "max": 34.99, "avg": 33.99 },
    "rating_range": { "min": 4.4, "max": 5.0, "avg": 4.7 },
    "prime_products": 0,
    "sponsored_products": 2
  }
}
```

## 🔍 Debug Analyzer Tool

Use the included `debug_analyzer.py` script to analyze exported data:

### Basic Analysis
```bash
python3 debug_analyzer.py
```

### Advanced Options
```bash
# Analyze specific directory
python3 debug_analyzer.py --dir custom_exports

# Analyze only API responses
python3 debug_analyzer.py --api-only

# Analyze only processed results
python3 debug_analyzer.py --processed-only

# Clean old files (older than 7 days)
python3 debug_analyzer.py --clean 7
```

### Sample Output
```
🔍 E-commerce Agent Debug Analyzer
==================================================
🔍 Analyzing API responses in: debug_exports
Found 1 API response files
Found 1 processed result files

📄 Analyzing: serpapi_response_wireless_earbuds_electronics_20250828_100241.json
   Timestamp: 2025-08-28T10:02:41.521465
   Search Query: wireless earbuds electronics
   Total Results: 22
   Has Error: False
   Amazon Total Results: 23893
   Page: 1
   Organic Results Count: 22
   Price Range: $9.98 - $89.99
   Average Price: $29.98
   Rating Range: 3.9 - 5.0
   Average Rating: 4.5
   Prime Products: 0
   Sponsored Products: 6
```

## 🐛 Debugging Common Issues

### 1. API Key Issues
**Symptoms**: Error messages in exports, `has_error: true`
**Check**: Look for error details in the raw API response files

### 2. Price Filtering Problems
**Symptoms**: Unexpected products in results
**Debug**: Compare price ranges in raw vs processed results

### 3. Rating Filtering Issues
**Symptoms**: Low-rated products in results
**Debug**: Check rating extraction in processed results

### 4. Missing Products
**Symptoms**: Fewer results than expected
**Debug**: Compare organic_results count vs processed_products count

## 📊 Data Analysis Tips

### Price Analysis
```python
# Extract prices from processed results
import json

with open('processed_results_*.json', 'r') as f:
    data = json.load(f)

prices = [p['price'] for p in data['processed_products']]
print(f"Price range: ${min(prices)} - ${max(prices)}")
```

### Rating Analysis
```python
# Extract ratings from raw API response
ratings = [r.get('rating', 0) for r in raw_response['organic_results']]
print(f"Rating distribution: {ratings}")
```

### Sales Analysis
```python
# Analyze monthly sales indicators
sales_data = [p.get('monthly_sales', 0) for p in processed_products]
print(f"Sales range: {min(sales_data)} - {max(sales_data)}")
```

## 🔒 Security Considerations

### File Protection
- Export files are automatically added to `.gitignore`
- Contains sensitive product data - don't commit to version control
- Consider encrypting exports for production use

### Data Cleanup
```bash
# Clean old exports regularly
python3 debug_analyzer.py --clean 7

# Or manually remove old files
find debug_exports -name "*.json" -mtime +7 -delete
```

## 🚀 Performance Monitoring

### API Response Times
Check `search_metadata.total_time_taken` in raw responses:
```json
"search_metadata": {
  "total_time_taken": 1.94
}
```

### Result Quality Metrics
Monitor these metrics in processed results:
- `total_products` vs expected count
- Price/rating distribution
- Prime/sponsored product ratios

## 🛠️ Customization

### Custom Export Directory
```python
# In your code
results = search_amazon_hot_products(
    keywords="your search",
    # ... other params
)
# Exports will use DEBUG_EXPORT_DIR from .env
```

### Conditional Exports
```python
# Only export on errors
if "error" in api_response:
    _export_api_response(api_response, query, "error_exports")
```

### Custom Analysis
Create your own analysis scripts using the exported JSON data:
```python
import json
import glob

def analyze_conversion_rates():
    files = glob.glob("debug_exports/serpapi_response_*.json")
    for file in files:
        with open(file, 'r') as f:
            data = json.load(f)
        # Your custom analysis here
```

## 📈 Best Practices

1. **Regular Cleanup**: Clean old exports weekly
2. **Monitor Trends**: Track API response times and result quality
3. **Error Analysis**: Always check exports when debugging issues
4. **Performance**: Disable exports in production if not needed
5. **Security**: Never commit export files to version control

## 🆘 Troubleshooting

### Exports Not Created
- Check `EXPORT_API_RESPONSES=true` in `.env`
- Verify directory permissions for `DEBUG_EXPORT_DIR`
- Check console output for export messages

### Large Export Files
- Limit `max_results` parameter in searches
- Clean old files regularly
- Consider compressing old exports

### Analysis Errors
- Ensure JSON files are valid (not corrupted)
- Check file permissions
- Verify Python dependencies are installed

---

This debug export system provides comprehensive visibility into the SerpAPI integration, making it easy to troubleshoot issues and optimize performance.
