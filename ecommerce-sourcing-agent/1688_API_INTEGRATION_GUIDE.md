# 1688 API Integration Guide

This guide explains the implementation of real 1688 supplier search using TMAPI service for the e-commerce product sourcing agent.

## 🚀 Implementation Overview

The `find_1688_suppliers` function has been upgraded from mock data to real API integration using TMAPI's 1688 image search service.

### ✅ **COMPLETED FEATURES**

1. **Real TMAPI Integration**: Uses http://api.tmapi.top/1688/search/image
2. **Image-Based Search**: Searches 1688 using product images from Amazon results
3. **Intelligent Fallback**: Gracefully falls back to mock data when API is unavailable
4. **Debug Export**: Exports both raw API responses and processed results
5. **Error Handling**: Comprehensive error handling with detailed logging
6. **Data Transformation**: Converts 1688 API response to standardized supplier format

## 🔧 Configuration

### Environment Variables

Add to your `.env` file:

```bash
# TMAPI 1688 Configuration
TMAPI_1688_TOKEN=your_tmapi_token_here

# Debug Export Configuration
EXPORT_API_RESPONSES=true
DEBUG_EXPORT_DIR=debug_exports
```

### API Token Setup

The integration uses TMAPI service:
- **Service**: TMAPI 1688 Search API
- **Endpoint**: http://api.tmapi.top/1688/search/image
- **Documentation**: https://tmapi.top/docs/ali/search/search-items-by-image-url
- **Authentication**: JWT token in query parameters

## 📊 API Response Structure

### Raw 1688 API Response

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "page": 1,
    "page_size": 20,
    "total_count": 2160,
    "items": [
      {
        "item_id": 651293202973,
        "product_url": "https://detail.1688.com/offer/651293202973.html",
        "title": "Product Title",
        "img": "https://cbu01.alicdn.com/img/...",
        "price": "0.50",
        "price_info": {
          "wholesale_price": "0.50",
          "origin_price": "0.50"
        },
        "quantity_begin": 300,
        "shop_info": {
          "company_name": "Company Name",
          "location": ["广东", "广州市"],
          "is_factory": true,
          "tp_year": 3
        }
      }
    ]
  }
}
```

### Transformed Supplier Format

```json
{
  "supplier_name": "Golden Dragon Manufacturing",
  "company_name": "Shenzhen Golden Dragon Electronics Co., Ltd.",
  "wholesale_price": 7.50,
  "minimum_order_quantity": 500,
  "supplier_rating": 4.8,
  "years_in_business": 12,
  "location": "Shenzhen, Guangdong",
  "contact_info": {
    "shop_url": "https://shop1234.1688.com",
    "member_id": "b2b-golden-dragon",
    "login_id": "Golden Dragon Manufacturing"
  },
  "product_images": ["https://1688.com/supplier1/image1.jpg"],
  "shipping_cost": 2.50,
  "delivery_time": "15-20 days",
  "certifications": ["Factory Direct", "TP Member"],
  "trade_assurance": true,
  "is_factory": true,
  "goods_score": 5
}
```

## 🔍 How It Works

### 1. Image-Based Search Process

```python
# 1. Get product image URL (from Amazon search results)
image_url = get_amazon_product_image()

# 2. Call TMAPI 1688 search API
response = requests.get("http://api.tmapi.top/1688/search/image", params={
    "apiToken": api_token,
    "img_url": image_url,
    "page": 1,
    "page_size": 20,
    "sort": "default"
})

# 3. Transform results to standardized format
suppliers = [transform_1688_result(item) for item in response.json()["data"]["items"]]
```

### 2. Data Transformation Pipeline

1. **Extract Basic Info**: Title, price, images, product URL
2. **Process Shop Info**: Company name, location, factory status
3. **Calculate Ratings**: Composite rating from goods score and business years
4. **Estimate Costs**: Shipping costs based on location and price
5. **Add Certifications**: Factory status, TP membership, trade assurance

### 3. Filtering and Sorting

```python
# Filter by criteria
filtered_suppliers = [
    supplier for supplier in suppliers
    if meets_criteria(supplier, target_price, min_order_qty, location_pref)
]

# Sort by rating and price
filtered_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))
```

## 🛠️ Error Handling

### API Rate Limiting (439 Error)

```python
if response.status_code == 439:
    print("⚠️  API rate limit reached, falling back to mock data")
    return get_mock_suppliers()
```

### Invalid API Token

```python
if not api_token:
    print("⚠️  TMAPI_1688_TOKEN not set, falling back to mock data")
    return get_mock_suppliers()
```

### Network Errors

```python
try:
    response = requests.get(url, params=params, timeout=30)
    response.raise_for_status()
except requests.exceptions.RequestException as e:
    print(f"❌ API request failed: {e}")
    return get_mock_suppliers()
```

## 📁 Debug Export Files

When `EXPORT_API_RESPONSES=true`, the system creates:

### 1. Raw API Response Export
**File**: `1688_api_response_{query}_{timestamp}.json`

Contains:
- Complete TMAPI response
- Search metadata
- Error information
- Request parameters

### 2. Processed Results Export
**File**: `1688_processed_results_{query}_{timestamp}.json`

Contains:
- Transformed supplier data
- Statistical summary
- Filtering results
- Performance metrics

## 🧪 Testing

### Basic Function Test

```python
from ecommerce_sourcing_agent import find_1688_suppliers

results = find_1688_suppliers(
    product_title="wireless earbuds bluetooth",
    target_price=25.0,
    minimum_order_qty=500,
    supplier_location="Guangdong",
    max_suppliers=3
)

print(f"Found {len(results)} suppliers")
```

### API Integration Test

```bash
# Test with real API (may hit rate limits)
python3 -c "
from ecommerce_sourcing_agent import find_1688_suppliers
results = find_1688_suppliers('bluetooth headphones', 30.0, 1000, 'any', 5)
print(f'Results: {len(results)} suppliers')
"
```

## 🔄 Integration with Amazon Search

The 1688 integration is designed to work with Amazon search results:

```python
# 1. Search Amazon for products
amazon_products = search_amazon_hot_products("electronics", "bluetooth headphones")

# 2. Use Amazon product images for 1688 search
for product in amazon_products:
    suppliers = find_1688_suppliers(
        product_title=product["title"],
        target_price=product["price"] * 0.3,  # 30% of retail price
        minimum_order_qty=1000,
        supplier_location="any",
        max_suppliers=5
    )
```

## 📈 Performance Considerations

### API Rate Limits
- TMAPI has rate limiting (439 errors)
- Implement exponential backoff for production use
- Cache results to reduce API calls

### Response Times
- Typical response time: 2-5 seconds
- Timeout set to 30 seconds
- Consider async requests for multiple searches

### Data Quality
- Image quality affects search accuracy
- Higher resolution images yield better results
- Product category matching improves relevance

## 🚀 Production Deployment

### 1. API Key Management
```bash
# Use secure environment variable management
export TMAPI_1688_TOKEN="your_production_token"
```

### 2. Error Monitoring
```python
# Add logging for production monitoring
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Log API failures
logger.error(f"1688 API failed: {error_message}")
```

### 3. Caching Strategy
```python
# Implement Redis caching for frequent searches
import redis
cache = redis.Redis()

cache_key = f"1688_search:{image_hash}"
cached_result = cache.get(cache_key)
if cached_result:
    return json.loads(cached_result)
```

## 🔧 Customization

### Custom Filtering Logic
```python
def custom_supplier_filter(supplier, criteria):
    # Add custom business logic
    if criteria.get("factory_only") and not supplier["is_factory"]:
        return False
    
    if criteria.get("min_years") and supplier["years_in_business"] < criteria["min_years"]:
        return False
    
    return True
```

### Enhanced Data Enrichment
```python
def enrich_supplier_data(supplier):
    # Add additional data sources
    supplier["market_reputation"] = get_market_reputation(supplier["company_name"])
    supplier["export_history"] = get_export_history(supplier["member_id"])
    return supplier
```

---

The 1688 API integration provides a robust foundation for supplier sourcing with real-time data, intelligent fallbacks, and comprehensive debugging capabilities.
